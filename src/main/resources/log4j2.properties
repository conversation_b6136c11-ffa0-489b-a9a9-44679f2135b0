# Set the status of the logging system
status=warn
packages=io.opentelemetry.instrumentation.log4j.appender.v2_17

# Define properties
property.logAttributes=%X
property.serviceName=${spring:spring.application.name:-user-bff}
property.environment=${env:ENVIRONMENT:-local}

# Define the console appender
appender.console.type=Console
appender.console.name=LogToConsole
appender.console.layout.type=PatternLayout
appender.console.layout.pattern=%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg [logAttributes] %n

# Define the rolling file appender
appender.rolling.type=RollingFile
appender.rolling.name=LogToRollingFile
appender.rolling.fileName=logs/Log.log
appender.rolling.filePattern=logs/log/$${date:yyyy-MM}/app-%d{MM-dd-yyyy}-%i.log.gz
appender.rolling.layout.type=PatternLayout
appender.rolling.layout.pattern=%d{yyyy-MM-dd HH:mm:ss} %m%n
appender.rolling.policies.type=Policies
appender.rolling.policies.time.type=TimeBasedTriggeringPolicy
appender.rolling.policies.size.type=SizeBasedTriggeringPolicy
appender.rolling.policies.size.size=10MB
appender.rolling.strategy.type=DefaultRolloverStrategy
appender.rolling.strategy.max=2000
appender.rolling.filter.threshold.type=LevelRangeFilter
appender.rolling.filter.threshold.minLevel=debug
appender.rolling.filter.threshold.maxLevel=debug

# Define the exception rolling file appender
appender.exception.type=RollingFile
appender.exception.name=LogErrorToRollingFile
appender.exception.fileName=logs/error.log
appender.exception.filePattern=logs/error/$${date:yyyy-MM}/app-%d{MM-dd-yyyy}-%i.log.gz
appender.exception.layout.type=PatternLayout
appender.exception.layout.pattern=%d{yyyy-MM-dd HH:mm:ss} %m%n
appender.exception.policies.type=Policies
appender.exception.policies.time.type=TimeBasedTriggeringPolicy
appender.exception.policies.size.type=SizeBasedTriggeringPolicy
appender.exception.policies.size.size=10MB
appender.exception.strategy.type=DefaultRolloverStrategy
appender.exception.strategy.max=1000
appender.exception.filter.threshold.type=LevelRangeFilter
appender.exception.filter.threshold.minLevel=error
appender.exception.filter.threshold.maxLevel=error

# Define the ELK JSON appender
appender.elk.type=RollingFile
appender.elk.name=ELKJsonAppender
appender.elk.fileName=logs/application.json
appender.elk.filePattern=logs/json/$${date:yyyy-MM}/app-%d{MM-dd-yyyy}-%i.json.gz
appender.elk.layout.type=JsonTemplateLayout
appender.elk.layout.eventTemplateUri=classpath:EcsLayout.json
appender.elk.layout.stackTraceEnabled=true
appender.elk.layout.locationInfoEnabled=true
appender.elk.policies.type=Policies
appender.elk.policies.time.type=TimeBasedTriggeringPolicy
appender.elk.policies.size.type=SizeBasedTriggeringPolicy
appender.elk.policies.size.size=50MB
appender.elk.strategy.type=DefaultRolloverStrategy
appender.elk.strategy.max=100

# Define the OpenTelemetry appender
appender.opentelemetry.type=OpenTelemetry
appender.opentelemetry.name=OpenTelemetryAppender
appender.opentelemetry.captureMapMessageAttributes=true
appender.opentelemetry.captureExperimentalAttributes=true
appender.opentelemetry.captureMarkerAttribute=true
appender.opentelemetry.captureContextDataAttributes=*

# Define the logger for the application
logger.app.name=com.userbff
logger.app.level=debug
logger.app.additivity=false
logger.app.appenderRefs=rolling,exception,elk,OpenTelemetryAppender
logger.app.appenderRef.rolling.ref=LogToRollingFile
logger.app.appenderRef.exception.ref=LogErrorToRollingFile
logger.app.appenderRef.elk.ref=ELKJsonAppender
logger.app.appenderRef.OpenTelemetryAppender.ref=OpenTelemetryAppender

# Define the root logger
rootLogger.level=info
rootLogger.appenderRefs=LogToConsole,elk,OpenTelemetryAppender
rootLogger.appenderRef.LogToConsole.ref=LogToConsole
rootLogger.appenderRef.elk.ref=ELKJsonAppender
rootLogger.appenderRef.OpenTelemetryAppender.ref=OpenTelemetryAppender

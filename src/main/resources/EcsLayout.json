{"@timestamp": {"$resolver": "timestamp", "pattern": {"format": "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", "timeZone": "UTC"}}, "log.level": {"$resolver": "level", "field": "name"}, "message": {"$resolver": "message", "stringified": true}, "service": {"name": "${serviceName}", "environment": "${environment}"}, "host": {"name": {"$resolver": "hostname"}}, "process": {"thread": {"name": {"$resolver": "thread", "field": "name"}}}, "log": {"logger": {"$resolver": "logger", "field": "name"}}, "labels": {"$resolver": "mdc", "flatten": true, "stringified": true}, "tags": {"$resolver": "ndc"}, "error": {"$resolver": "exception", "field": {"type": "className", "message": "message", "stack_trace": "stackTrace"}}, "trace": {"id": {"$resolver": "mdc", "key": "traceId"}}, "span": {"id": {"$resolver": "mdc", "key": "spanId"}}, "ecs": {"version": "1.12.0"}}
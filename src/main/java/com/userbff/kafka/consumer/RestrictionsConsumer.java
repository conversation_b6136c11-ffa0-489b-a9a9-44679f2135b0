package com.userbff.kafka.consumer;

import static com.userbff.enums.RequestStatus.FAILURE;
import static com.userbff.enums.RequestStatus.REQUEST;
import static com.userbff.enums.RequestStatus.SUCCESS;
import static com.userbff.util.MetricsConstants.CONSUMER;
import static com.userbff.util.MetricsConstants.ERROR_INFO_TAG;
import static com.userbff.util.MetricsConstants.KAFKA_EVENT;
import static com.userbff.util.MetricsConstants.KAFKA_EVENT_TIMER;
import static com.userbff.util.MetricsConstants.KAFKA_STREAM_TYPE_TAG;
import static com.userbff.util.MetricsConstants.STATUS_TAG;
import static com.userbff.util.MetricsConstants.TOPIC_TAG;

import com.userbff.config.KafkaConfig;
import com.userbff.model.restrictions.RestrictionsUpdateEvent;
import com.userbff.service.UserServiceFacade;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.sentry.Sentry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class RestrictionsConsumer {
    private final UserServiceFacade userServiceFacade;
    private final MeterRegistry meterRegistry;

    @KafkaListener(topics = KafkaConfig.RESTRICTIONS_UPDATE_TOPIC, groupId = "${spring.kafka.consumer.group-id}")
    public void handleDocumentUpdate(RestrictionsUpdateEvent message) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            log.debug(
                    "Received document update event: {} from queue: {}",
                    message,
                    KafkaConfig.RESTRICTIONS_UPDATE_TOPIC);
            meterRegistry
                    .counter(
                            KAFKA_EVENT,
                            KAFKA_STREAM_TYPE_TAG,
                            CONSUMER,
                            TOPIC_TAG,
                            KafkaConfig.RESTRICTIONS_UPDATE_TOPIC,
                            STATUS_TAG,
                            REQUEST.getStatus())
                    .increment();

            userServiceFacade.updateUserRestrictions(message);

            log.debug(
                    "Successfully processed document update event: {} from queue: {}",
                    message,
                    KafkaConfig.RESTRICTIONS_UPDATE_TOPIC);
            meterRegistry
                    .counter(
                            KAFKA_EVENT,
                            KAFKA_STREAM_TYPE_TAG,
                            CONSUMER,
                            TOPIC_TAG,
                            KafkaConfig.RESTRICTIONS_UPDATE_TOPIC,
                            STATUS_TAG,
                            SUCCESS.getStatus())
                    .increment();
        } catch (Exception e) {
            Sentry.captureException(e);
            log.error(
                    "Error processing document update event: {} from queue: {}",
                    message,
                    KafkaConfig.RESTRICTIONS_UPDATE_TOPIC,
                    e);
            meterRegistry
                    .counter(
                            KAFKA_EVENT,
                            KAFKA_STREAM_TYPE_TAG,
                            CONSUMER,
                            TOPIC_TAG,
                            KafkaConfig.RESTRICTIONS_UPDATE_TOPIC,
                            STATUS_TAG,
                            FAILURE.getStatus(),
                            ERROR_INFO_TAG,
                            e.getMessage())
                    .increment();
        } finally {
            sample.stop(meterRegistry.timer(
                    KAFKA_EVENT_TIMER,
                    KAFKA_STREAM_TYPE_TAG,
                    CONSUMER,
                    TOPIC_TAG,
                    KafkaConfig.RESTRICTIONS_UPDATE_TOPIC));
        }
    }
}

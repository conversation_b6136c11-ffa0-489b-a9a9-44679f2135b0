input {
  file {
    path => "/usr/share/logstash/logs/application.json"
    start_position => "beginning"
    codec => "json"
    type => "user-bff-logs"
  }
}

filter {
  if [type] == "user-bff-logs" {
    # Parse timestamp
    date {
      match => [ "@timestamp", "yyyy-MM-dd'T'HH:mm:ss.SSSXXX" ]
      target => "@timestamp"
    }
    
    # Add service metadata
    mutate {
      add_field => { "service_name" => "user-bff" }
      add_field => { "service_version" => "${SERVICE_VERSION:unknown}" }
      add_field => { "environment" => "${ENVIRONMENT:local}" }
    }
    
    # Parse log level
    if [log.level] {
      mutate {
        add_field => { "level" => "%{[log.level]}" }
      }
    }
    
    # Extract trace information
    if [trace.id] {
      mutate {
        add_field => { "trace_id" => "%{[trace.id]}" }
      }
    }
    
    if [span.id] {
      mutate {
        add_field => { "span_id" => "%{[span.id]}" }
      }
    }
    
    # Parse HTTP request information from labels
    if [labels] {
      if [labels][request.method] {
        mutate {
          add_field => { "http_method" => "%{[labels][request.method]}" }
        }
      }
      
      if [labels][request.uri] {
        mutate {
          add_field => { "http_uri" => "%{[labels][request.uri]}" }
        }
      }
      
      if [labels][response.status] {
        mutate {
          add_field => { "http_status" => "%{[labels][response.status]}" }
        }
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["${ELASTICSEARCH_HOSTS:elasticsearch:9200}"]
    index => "user-bff-logs-%{+YYYY.MM.dd}"
    template_name => "user-bff-logs"
    template => "/usr/share/logstash/templates/user-bff-template.json"
    template_overwrite => true
  }
  
  # Debug output (remove in production)
  stdout {
    codec => rubydebug
  }
}
